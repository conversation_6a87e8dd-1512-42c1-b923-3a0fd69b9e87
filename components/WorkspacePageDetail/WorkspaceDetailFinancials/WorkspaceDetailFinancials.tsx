'use client';

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation";
import { getPropertyFinancialsDB, getPropertyFinancialsUnits, updatePropertyFinancials } from "@/actions/propertyFinancials";
import { usePortfolio } from "@/context/PortfolioContext";
import { getPortfolioFinancials, getPortfolioFinancialsAggregated, updatePortfolioFinancials, refreshPortfolioFinancials } from "@/actions/portfolioActions";
import WorkspaceDetailFinancialsItem from "./WorkspaceDetailFinancialsItem/WorkspaceDetailFinancialsItem";
import WorkspaceDetailFinancialsHeader from "./WoespaceDatailFinancialsHeader";
import Tooltip from "@/components/UI/Tooltip";
import { financialTooltips } from "@/constants/financialTooltips";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExpand, faCompress, faHexagonNodes, faSync } from "@fortawesome/free-solid-svg-icons";
import { useRouter } from "next/navigation";
import React from "react";
import Spinner from "@/components/UI/Spinner";
import WorkspaceDetailFinancialsExportPDF from './WorkspaceDetailFinancialsExportPDF';
import { FinanceCalculationService } from "@/services/financeCalculationService";
import { FinanceCalculationResponse } from "@/types/finance";

// Type definitions for financial sections
interface FinancialSectionItem {
	key: string;
	label: string;
	format: string;
	edit?: {isCalculated?: boolean, isAICalculation?: boolean};
	negative?: boolean;
	isSectionTotal?: boolean;
	isCalculated?: boolean;
	expandable?: boolean;
	subItems?: FinancialSectionItem[];
}

interface FinancialSection {
	title: string;
	items: FinancialSectionItem[];
}

interface FinancialYear {
	year: number;
	[key: string]: any;
}

// Add interface for metadata tracking
interface ChangeMetadata {
	source: 'manual' | 'calculator' | 'ai';
	timestamp: string;
	type: string;
	value: number;
	previousValue?: number;
}

interface FieldMetadata {
	sources: ChangeMetadata[];
}

// Parse formatted value back to raw number
const parseValue = (value: string): string => {
	if (!value) return '';
	// Remove $, % and thousand separators
	return value.replace(/[$,%]/g, '');
};

// Helper function to determine if a field should be stored as negative
const shouldBeNegative = (key: string): boolean => {
	const negativeFields = [
		// Loss fields
		'vacancy_loss', 'credit_loss',
		// Expense fields
		'property_tax', 'insurance', 'repairs', 'maintenance', 'professional_fees',
		'utilities', 'services', 'reserves', 'total_operating_expenses',
		'management_fees', 'leasing_fees', 'legal_fees', 'accounting_fees',
		'engineering_fees', 'marketing_fees', 'consulting_fees'
	];
	return negativeFields.includes(key);
};

const getFinancialSections = (marketId: string | null): FinancialSection[] => {
	if (marketId) {
	  return [
		{
		  title: "Income Projections",
		  items: [
			{ 
				key: 'rental_income', 
				label: 'Gross Scheduled Income', 
				format: 'currency',
				isCalculated: true,
				edit: {isCalculated: true, isAICalculation: false},
				expandable: true,
				subItems: [
					{ key: 'long_term_rental', label: 'Long Term Rental', format: 'currency', edit: {isCalculated: false, isAICalculation: true} },
					{ key: 'short_term_rental', label: 'Short Term Rental', format: 'currency', edit: {isCalculated: false, isAICalculation: false} }
				]
			},
			{ key: 'other_income', label: 'Other Income', format: 'currency', edit: {isCalculated: false, isAICalculation: true}} ,
			{ 
				key: 'vacancy_loss', 
				label: 'Vacancy Loss', 
				format: 'currency', 
				negative: true, 
				edit: {isCalculated: true, isAICalculation: true} 
			},
			{ 
				key: 'credit_loss', 
				label: 'Credit Loss', 
				format: 'currency', 
				negative: true, 
				edit: {isCalculated: false, isAICalculation: true} 
			},
			
			{ key: 'effective_gross_income', label: 'Effective Gross Income', format: 'currency', isSectionTotal: true, isCalculated: true } ,
		  ]
		},
		{
		  title: "Expense Projections",
		  items: [
			{
				key: 'property_tax',
				label: 'Property Taxes',
				format: 'currency',
				negative: true,
				edit: {isCalculated: false, isAICalculation: false}
			},
			{
				key: 'insurance',
				label: 'Insurance',
				format: 'currency',
				negative: true,
				edit: {isCalculated: false, isAICalculation: false}
			},
			{
				key: 'repairs',
				label: 'Repairs',
				format: 'currency',
				negative: true,
				edit: {isCalculated: false, isAICalculation: false}
			},
			{
				key: 'maintenance',
				label: 'Maintenance',
				format: 'currency',
				negative: true,
				edit: {isCalculated: false, isAICalculation: false}
			},
			{
				key: 'professional_fees',
				label: 'Professional Fees',
				format: 'currency',
				negative: true,
				isCalculated: true,
				edit: {isCalculated: true, isAICalculation: false},
				expandable: true,
				subItems: [
					{ key: 'management_fees', label: 'Management', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'leasing_fees', label: 'Leasing', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'legal_fees', label: 'Legal', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'accounting_fees', label: 'Accounting', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'engineering_fees', label: 'Engineering/Inspections', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'marketing_fees', label: 'Marketing/Advertising', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'consulting_fees', label: 'Consulting', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} }
				]
			},
			{
				key: 'utilities',
				label: 'Utilities',
				format: 'currency',
				negative: true,
				edit: {isCalculated: false, isAICalculation: false}
			},
			{
				key: 'services',
				label: 'Services',
				format: 'currency',
				negative: true,
				edit: {isCalculated: false, isAICalculation: false}
			},
			{ key: 'reserves', label: 'Reserves for Replacements', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} } ,

			{ key: 'total_operating_expenses', label: 'Total Operating Expenses', format: 'currency', negative: true, isSectionTotal: true, isCalculated: true } ,
		  ]
		},
		{
		  title: "Net Operating Income (NOI)",
		  items: [
			{ key: 'net_operating_income', label: 'Net Operating Income (NOI)', format: 'currency', edit: {isCalculated: true}, isCalculated: true } ,
		  ]
		},
		{
		  title: "Financing & Debt Service",
		  items: [
			{ key: 'annual_debt_service', label: 'Annual Debt Service (P&I)', format: 'currency', edit: {isCalculated: true, isAICalculation: true} } ,

			{ key: 'dscr', label: 'DSCR (NOI ÷ Debt Service)', format: 'multiplier', edit: {isCalculated: true}, isCalculated: true } ,
		  ]
		},
		{
		  title: "Cash Flow Analysis",
		  items: [
			{ key: 'cash_flow_before_taxes', label: 'Cash Flow Before Taxes', format: 'currency', edit: {isCalculated: true} } ,
			{ key: 'cash_flow_after_taxes', label: 'Cash Flow After Taxes', format: 'currency', edit: {isCalculated: false, isAICalculation: false} } ,
			{ key: 'cumulative_cash_flow', label: 'Cumulative Cash Flow', format: 'currency', edit: {isCalculated: true} } ,
		  ]
		},
		{
		  title: "Valuation Metrics",
		  items: [
			{key: 'property_value', label: 'Property Value', format: 'currency', edit: {isCalculated: false, isAICalculation: false} },
			{ key: 'cap_rate', label: 'Cap Rate (%)', format: 'percentage', isCalculated: true } ,
			{ key: 'gross_rent_multiplier', label: 'GRM (×)', format: 'multiplier', edit: {isCalculated: false, isAICalculation: false} } ,
			{ key: 'equity_multiple', label: 'Equity Multiple (×)', format: 'multiplier', edit: {isCalculated: false, isAICalculation: false} } ,
			{ key: 'cash_on_cash_return', label: 'Cash-on-Cash Return (%)', format: 'percentage', edit: {isCalculated: false, isAICalculation: false} } ,
		  ]
		}
	];
	} else {
	  // Portfolio level financial sections - mark all as non-editable
	  return [
		{
		  title: "Income Projections",
		  items: [
			{ 
				key: 'rental_income', 
				label: 'Gross Scheduled Income', 
				format: 'currency', 
				edit: {isCalculated: true, isAICalculation: false}, // Mark as calculated (non-editable)
				expandable: true,
				subItems: [
					{ key: 'long_term_rental', label: 'Long Term Rental', format: 'currency', edit: {isCalculated: true, isAICalculation: false} },
					{ key: 'short_term_rental', label: 'Short Term Rental', format: 'currency', edit: {isCalculated: true, isAICalculation: false} }
				]
			},
			{ key: 'other_income', label: 'Other Income', format: 'currency', edit: {isCalculated: true, isAICalculation: false} } ,
			{ 
				key: 'vacancy_loss', 
				label: 'Vacancy Loss', 
				format: 'currency', 
				negative: true, 
				edit: {isCalculated: true, isAICalculation: false} 
			},
			{ 
				key: 'credit_loss', 
				label: 'Credit Loss', 
				format: 'currency', 
				negative: true, 
				edit: {isCalculated: true, isAICalculation: false} 
			},

			{ key: 'effective_gross_income', label: 'Effective Gross Income', format: 'currency', isSectionTotal: true, isCalculated: true } ,
		  ]
		},
		{
		  title: "Expense Projections",
		  items: [
			{
				key: 'property_tax',
				label: 'Property Taxes',
				format: 'currency',
				negative: true,
				edit: {isCalculated: true, isAICalculation: false}
			},
			{
				key: 'insurance',
				label: 'Insurance',
				format: 'currency',
				negative: true,
				edit: {isCalculated: true, isAICalculation: false}
			},
			{
				key: 'repairs',
				label: 'Repairs',
				format: 'currency',
				negative: true,
				edit: {isCalculated: true, isAICalculation: false}
			},
			{
				key: 'maintenance',
				label: 'Maintenance',
				format: 'currency',
				negative: true,
				edit: {isCalculated: true, isAICalculation: false}
			},
			{
				key: 'professional_fees',
				label: 'Professional Fees',
				format: 'currency',
				negative: true,
				isCalculated: true,
				expandable: true,
				subItems: [
					{ key: 'management_fees', label: 'Management', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'leasing_fees', label: 'Leasing', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'legal_fees', label: 'Legal', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'accounting_fees', label: 'Accounting', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'engineering_fees', label: 'Engineering/Inspections', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'marketing_fees', label: 'Marketing/Advertising', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} },
					{ key: 'consulting_fees', label: 'Consulting', format: 'currency', negative: true, edit: {isCalculated: false, isAICalculation: false} }
				]
			},
			{
				key: 'utilities',
				label: 'Utilities',
				format: 'currency',
				negative: true,
				edit: {isCalculated: true, isAICalculation: false}
			},
			{
				key: 'services',
				label: 'Services',
				format: 'currency',
				negative: true,
				edit: {isCalculated: true, isAICalculation: false}
			},
			{ key: 'reserves', label: 'Reserves for Replacements', format: 'currency', negative: true, edit: {isCalculated: true, isAICalculation: false} } ,

			{ key: 'total_operating_expenses', label: 'Total Operating Expenses', format: 'currency', negative: true, isSectionTotal: true, isCalculated: true } ,
		  ]
		},
		{
		  title: "Net Operating Income (NOI)",
		  items: [
			{ key: 'net_operating_income', label: 'Net Operating Income (NOI)', format: 'currency', isSectionTotal: true, isCalculated: true } ,
		  ]
		},
		{
		  title: "Financing & Debt Service",
		  items: [
			{ key: 'annual_debt_service', label: 'Annual Debt Service (P&I)', format: 'currency', edit: {isCalculated: true, isAICalculation: false} } ,

			{ key: 'dscr', label: 'DSCR (NOI ÷ Debt Service)', format: 'multiplier', isCalculated: true } ,
		  ]
		},
		{
		  title: "Cash Flow Analysis",
		  items: [
			{ key: 'cash_flow_before_taxes', label: 'Cash Flow Before Taxes', format: 'currency', isSectionTotal: true, isCalculated: true } ,
			{ key: 'cash_flow_after_taxes', label: 'Cash Flow After Taxes', format: 'currency', edit: {isCalculated: true, isAICalculation: false} } ,
			{ key: 'cumulative_cash_flow', label: 'Cumulative Cash Flow', format: 'currency', isCalculated: true } ,
		  ]
		},
		{
		  title: "Valuation Metrics",
		  items: [
			{ key: 'cap_rate', label: 'Cap Rate (%)', format: 'percentage', isCalculated: true } ,
			{ key: 'gross_rent_multiplier', label: 'GRM (×)', format: 'multiplier', edit: {isCalculated: true, isAICalculation: false} } ,
			{ key: 'equity_multiple', label: 'Equity Multiple (×)', format: 'multiplier', edit: {isCalculated: true, isAICalculation: false} } ,
			{ key: 'cash_on_cash_return', label: 'Cash-on-Cash Return (%)', format: 'percentage', edit: {isCalculated: true, isAICalculation: false} } ,
		  ]
		},
		{
		  title: "Portfolio Summary & Roll-Up",
		  items: [
			{ key: 'total_acquisition_cost', label: 'Total Acquisition Cost', format: 'currency', edit: {isCalculated: true, isAICalculation: false} } ,
			{ key: 'aggregated_noi', label: 'Aggregated NOI', format: 'currency', edit: {isCalculated: true, isAICalculation: false} } ,
			{ key: 'blended_cap_rate', label: 'Blended Cap Rate (%)', format: 'percentage', edit: {isCalculated: true, isAICalculation: false} } ,
			{ key: 'portfolio_irr', label: 'Portfolio IRR (%)', format: 'percentage', edit: {isCalculated: true, isAICalculation: false} } ,
		  ]
		}
	  ];
	}
  };

  export default function WorkspaceDetailFinancials(){
	const { selectedPortfolio, properties } = usePortfolio()
	const searchParams = useSearchParams()
	const router = useRouter()
	const addressId = searchParams.get('addressId')
	const isFocusedParam = searchParams.get('focus') === 'true'
	const [financials, setFinancials] = useState<FinancialYear[]>([])
	const [units, setUnits] = useState<{ [key: string]: any }[]>([])
	const [isLoadingFinancials, setIsLoadingFinancials] = useState(false)
	const [isClient, setIsClient] = useState(false)
	const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
	const [isFocused, setIsFocused] = useState(isFocusedParam)
	const [inflationRate, setInflationRate] = useState(3.0)
	const [isAutoCalculating, setIsAutoCalculating] = useState(false)
	const [propertyInfo, setPropertyInfo] = useState({
		image: '/placeholder-property.jpg',
		name: 'Property',
		address: ''
	})
	const [isCalculatingExpenses, setIsCalculatingExpenses] = useState(false)
	const [isRefreshingAggregation, setIsRefreshingAggregation] = useState(false)
	const [expenseCalculationTaskToken, setExpenseCalculationTaskToken] = useState<string | null>(null)
	const [expenseCalculationMessage, setExpenseCalculationMessage] = useState<string>('')
	const [portfolioCompletion, setPortfolioCompletion] = useState(0)
	const [isAutoRecalculating, setIsAutoRecalculating] = useState(false)
	
	// Handle client-side hydration
	useEffect(() => {
		setIsClient(true)
	}, [])

	// Get financial sections for display
	const sections = getFinancialSections(addressId);
	
	// Flatten all items for easier access (including sub-items)
	const allItems = sections.flatMap(section => 
		section.items.flatMap(item => 
			item.subItems ? [item, ...item.subItems] : [item]
		)
	);
	
	// Handle focus toggle
	const handleFocusToggle = () => {
		if (isFocused) {
			// Return to normal view by removing focus parameter
			const newSearchParams = new URLSearchParams(searchParams.toString())
			newSearchParams.delete('focus')
			router.push(`${window.location.pathname}?${newSearchParams.toString()}`)
		} else {
			// Add focus parameter to current URL
			const newSearchParams = new URLSearchParams(searchParams.toString())
			newSearchParams.set('focus', 'true')
			router.push(`${window.location.pathname}?${newSearchParams.toString()}`)
		}
	}

	// Check if we're in focus mode from URL
	useEffect(() => {
		const focus = searchParams.get('focus')
		setIsFocused(focus === 'true')
	}, [searchParams])

	useEffect(() => {
		setIsLoadingFinancials(true)
		if(addressId) {
			const fetchFinancials = async () => {
				const financials = await getPropertyFinancialsDB(addressId as string);
				setFinancials(financials)
				setIsLoadingFinancials(false)
			}
			fetchFinancials();

			getPropertyFinancialsUnits(addressId as string).then((data) => {
				setUnits(data)
			})
		}

		if(!addressId && selectedPortfolio) {
			// Use aggregated financial data for portfolio-wide view
			getPortfolioFinancialsAggregated(selectedPortfolio?.id).then((data) => {
				setFinancials(data as FinancialYear[])
				setIsLoadingFinancials(false)
			}).catch((error) => {
				console.error('Error loading aggregated portfolio financials:', error);
				// Fallback to regular portfolio financials
				getPortfolioFinancials(selectedPortfolio?.id).then((data) => {
					setFinancials(data as FinancialYear[])
					setIsLoadingFinancials(false)
				})
			})
		}

	}, [addressId, selectedPortfolio]);

	// // Fetch property info when component mounts
	// useEffect(() => {
	// 	if (addressId) {
	// 		fetch(`/api/property/${addressId}`)
	// 			.then(res => res.json())
	// 			.then(data => {
	// 				setPropertyInfo({
	// 					image: data.image_url || '/placeholder-property.jpg',
	// 					name: data.property_name || 'Property',
	// 					address: data.address || ''
	// 				});
	// 			})
	// 			.catch(error => {
	// 				console.error('Error fetching property info:', error);
	// 			});
	// 	}
	// }, [addressId]);

	// Helper function to get value for a specific year and category
	const getValue = (year: number, category: string) => {
		const yearData = financials.find(item => item.year === year);
		return yearData ? yearData[category] || '' : '';
	};



	// Helper function to refresh financials from database
	const refreshFinancialsFromDB = async () => {
		try {
			if (addressId) {
				const freshFinancials = await getPropertyFinancialsDB(addressId as string);
				setFinancials(freshFinancials);
				return freshFinancials;
			} else if (selectedPortfolio) {
				const freshFinancials = await getPortfolioFinancialsAggregated(selectedPortfolio.id);
				setFinancials(freshFinancials as FinancialYear[]);
				return freshFinancials;
			}
		} catch (error) {
			console.error('Error refreshing financials from database:', error);
		}
		return null;
	};

	// Helper function to get metadata for a specific year
	const getMetadataForYear = (year: number) => {
		const yearData = financials.find(item => item.year === year);
		return yearData?.metadata;
	};

	// Apply inflation adjustment to a value based on year
	const getInflationAdjustedValue = (baseValue: number, year: number) => {
		if (year > 1) {
			const rate = inflationRate / 100;
			const compound = Math.pow(1 + rate, year - 1);
			return Math.round(baseValue * compound);
		}
		return baseValue;
	};

	// Auto-recalculate GSI-dependent fields when Year 1 GSI changes
	const autoRecalculateYear1GSIDependentFields = async (year1GSI: number) => {
		console.log('Auto-recalculating GSI-dependent fields for Year 1 GSI:', year1GSI);

		const defaultRates = {
			rental_income: 1,
			other_income: 0.03,    // 3%
			vacancy_loss: 0.05,    // 5% (negative)
			credit_loss: 0.02      // 2% (negative)
		};

		const batchValues: { year: number; key: string; value: string; source: 'human' | 'ai' | 'calculator' }[] = [];

		// Calculate for all dependent fields across all years
		Object.entries(defaultRates).forEach(([fieldKey, rate]) => {
			for (let targetYear = 1; targetYear <= 5; targetYear++) {
				// Calculate GSI for this year (Year 1 base + inflation adjustment)
				const yearGSI = targetYear === 1 ? year1GSI :
					getInflationAdjustedValue(year1GSI, targetYear);

				let calculatedValue = yearGSI * rate;

				// Apply negative for loss fields
				if (fieldKey === 'vacancy_loss' || fieldKey === 'credit_loss') {
					calculatedValue = -Math.abs(calculatedValue);
				}

				batchValues.push({
					year: targetYear,
					key: fieldKey,
					value: Math.round(calculatedValue).toString(),
					source: 'calculator' as const
				});
			}
		});

		return batchValues;
	};


	// Toggle expanded state for a row
	const toggleExpanded = (itemKey: string) => {
		setExpandedRows(prev => {
			const newExpanded = new Set(prev);
			if (newExpanded.has(itemKey)) {
				newExpanded.delete(itemKey);
			} else {
				newExpanded.add(itemKey);
			}
			return newExpanded;
		});
	};

	// Add smooth table glare effect
	const addTableGlareEffect = () => {
		const tables = document.querySelectorAll('.financial-table');
		tables.forEach((table) => {
			// Add glare effect class
			table.classList.add('table-glare-effect');
			
			// Remove effect after animation completes
			setTimeout(() => {
				table.classList.remove('table-glare-effect');
			}, 2000);
		});
	};

	// Calculate section totals for a specific year
	const calculateSectionTotal = (year: number, section: FinancialSection, getFreshValue?: (year: number, key: string) => string) => {
		// Get all non-section total items in the section
		const itemsToSum = section.items.filter(item => !item.isSectionTotal);
		
		let total = 0;
		
		itemsToSum.forEach(item => {
			let itemValue = 0;
			
			// If item has sub-items, sum the sub-items instead of the parent
			if (item.subItems && item.subItems.length > 0) {
				item.subItems.forEach(subItem => {
					const subValue = parseFloat(getFreshValue ? getFreshValue(year, subItem.key) : getValue(year, subItem.key));
					if (!isNaN(subValue)) {
						itemValue += subValue; // Always add, negative values will be handled by the value itself
					}
				});
			} else {
				itemValue = parseFloat(getFreshValue ? getFreshValue(year, item.key) : getValue(year, item.key));
				if (isNaN(itemValue)) itemValue = 0;
			}
			
			total += itemValue; // Always add, negative values will be handled by the value itself
		});
		
		return total;
	};

	// Calculate Net Operating Income (NOI) for a specific year
	const calculateNOI = (year: number): number => {
		// Get Effective Gross Income
		const effectiveGrossIncome = parseFloat(getValue(year, 'effective_gross_income')) || 0;

		// Get Total Operating Expenses (should be negative)
		const totalOperatingExpenses = parseFloat(getValue(year, 'total_operating_expenses')) || 0;

		// Calculate NOI: Effective Gross Income - Total Operating Expenses
		// Note: totalOperatingExpenses should already be negative, so we subtract it
		const noi = effectiveGrossIncome - Math.abs(totalOperatingExpenses);

		return noi;
	};


	// Remove this problematic useEffect that causes infinite loops
	// Section totals are now calculated dynamically in calculateSectionTotal function	
	

	const handleSaveFinancials = async (data: { [key: string]: number }, year: number, source: 'human' | 'ai' | 'calculator' = 'human') => {
		try {
			const timestamp = new Date().toISOString();
			const metadata: { [key: string]: FieldMetadata } = {};

			// Convert numbers to strings for saving, but filter out invalid values
			const stringData: { [key: string]: string } = {};
			Object.entries(data).forEach(([key, value]) => {
				// Only include valid numeric values
				if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
					stringData[key] = value.toString();
					
					const previousValue = financials.find(f => f.year === year)?.[key];
					
					const changeMetadata: ChangeMetadata = {
						source: source === 'human' ? 'manual' : source === 'calculator' ? 'calculator' : 'ai',
						timestamp,
						type: key,
						value,
						previousValue: previousValue !== undefined ? Number(previousValue) : undefined
					};

					metadata[key] = {
						sources: [changeMetadata]
					};
				} else {
					console.warn(`Skipping invalid value for ${key}:`, value);
				}
			});

			// Only proceed if we have valid data to save
			if (Object.keys(stringData).length === 0) {
				console.warn('No valid data to save, skipping database update');
				return;
			}

			// Map 'calculator' to 'ai' for backend compatibility
			const backendSource: 'human' | 'ai' = source === 'calculator' ? 'ai' : source === 'human' ? 'human' : 'ai';

			let result;
			if(addressId) {
				result = await updatePropertyFinancials(addressId as string, stringData, year, backendSource);
			} else if(!addressId && selectedPortfolio) {
				result = await updatePortfolioFinancials(selectedPortfolio.id, stringData, year, backendSource);
			}

			if (result?.success) {
				// Update local state to match saved data
				setFinancials(prevFinancials => 
					prevFinancials.map(item => {
						if (item.year === year) {
							return {
								...item,
								...Object.fromEntries(
									Object.entries(stringData).map(([key, value]) => [key, parseFloat(value)])
								)
							};
						}
						return item;
					})
				);
				console.log('Financial data saved successfully with metadata:', metadata);
			} else {
				console.error('Failed to save financial data');
			}
		} catch (error) {
			console.error('Error saving financial data:', error);
			// Optionally revert the local state here if save failed
		}
	}

	// Auto-recalculation for derived fields
	const recalculateAndSaveDerivedFields = async (year: number, skipSave: boolean = false) => {
		setIsAutoCalculating(true);
		
		const calculatedFields: { [key: string]: string } = {};
		const freshfinancials = await refreshFinancialsFromDB();
		function getFreshValue(year: number, key: string) {
			return freshfinancials?.find(f => f.year === year)?.[key] || '';
		}

		// Calculate rental income (Gross Scheduled Income) from sub-items
		const longTermRental = parseFloat(getFreshValue(year, 'long_term_rental')) || 0;
		const shortTermRental = parseFloat(getFreshValue(year, 'short_term_rental')) || 0;
		const rentalIncome = longTermRental + shortTermRental;
		if (rentalIncome > 0) {
			calculatedFields['rental_income'] = rentalIncome.toString();
		}
		
		// Calculate professional fees from sub-items
		const managementFees = parseFloat(getFreshValue(year, 'management_fees')) || 0;
		const leasingFees = parseFloat(getFreshValue(year, 'leasing_fees')) || 0;
		const legalFees = parseFloat(getFreshValue(year, 'legal_fees')) || 0;
		const accountingFees = parseFloat(getFreshValue(year, 'accounting_fees')) || 0;
		const engineeringFees = parseFloat(getFreshValue(year, 'engineering_fees')) || 0;
		const marketingFees = parseFloat(getFreshValue(year, 'marketing_fees')) || 0;
		const consultingFees = parseFloat(getFreshValue(year, 'consulting_fees')) || 0;
		const professionalFees = managementFees + leasingFees + legalFees + accountingFees + engineeringFees + marketingFees + consultingFees;
		if (professionalFees > 0) {
			calculatedFields['professional_fees'] = professionalFees.toString();
		}
		
		// Calculate section totals for all sections (EXCEPT NOI which is calculated manually)
		sections.forEach(section => {
			const sectionTotalItem = section.items.find(item => item.isSectionTotal);
			if (sectionTotalItem && sectionTotalItem.key !== 'net_operating_income') {
				const total = calculateSectionTotal(year, section, getFreshValue);
				calculatedFields[sectionTotalItem.key] = total.toString();
			}
		});
		
		// Calculate NOI manually (NOT as section total) - NOI = Effective Gross Income - Total Operating Expenses
		const effectiveGrossIncome = parseFloat(getFreshValue(year, 'effective_gross_income')) || 0;
		const totalOperatingExpenses = Math.abs(parseFloat(getFreshValue(year, 'total_operating_expenses')) || 0);
		const noi = effectiveGrossIncome - totalOperatingExpenses;
		calculatedFields['net_operating_income'] = noi.toString();
		
		// Calculate Cash Flow Before Taxes (NOI - Debt Service)
		const annualDebtService = parseFloat(getFreshValue(year, 'annual_debt_service')) || 0;
		const cashFlowBeforeTaxes = noi - annualDebtService;
		calculatedFields['cash_flow_before_taxes'] = cashFlowBeforeTaxes.toString();
		
		// Calculate DSCR (NOI ÷ Debt Service)
		if (annualDebtService > 0) {
			const dscr = noi / annualDebtService;
			calculatedFields['dscr'] = dscr.toFixed(2);
		}
		
		// Calculate Cap Rate (for property level only)
		if (addressId) {
			const propertyValue = parseFloat(getFreshValue(year, 'property_value')) || 0;
			if (propertyValue > 0 && noi > 0) {
				const capRate = (noi / propertyValue) * 100;
				calculatedFields['cap_rate'] = capRate.toFixed(2);
				console.log(`Calculated Cap Rate for year ${year}: ${capRate.toFixed(2)}% (NOI: ${noi}, Property Value: ${propertyValue})`);
			} else {
				console.log(`Skipping Cap Rate calculation for year ${year}: NOI=${noi}, Property Value=${propertyValue}`);
			}
		}
		
	// Calculate Cumulative Cash Flow (simplified - would need previous years)
	const cashFlowAfterTaxes = parseFloat(getFreshValue(year, 'cash_flow_after_taxes')) || cashFlowBeforeTaxes;
	let cumulativeCashFlow = cashFlowAfterTaxes;
	
	// Add previous years' cash flows
	for (let prevYear = 1; prevYear < year; prevYear++) {
		const prevCashFlow = parseFloat(getFreshValue(prevYear, 'cash_flow_after_taxes')) || parseFloat(getFreshValue(prevYear, 'cash_flow_before_taxes')) || 0;
		cumulativeCashFlow += prevCashFlow;
	}
	calculatedFields['cumulative_cash_flow'] = cumulativeCashFlow.toString();
	
	// Update state with calculated values
	setFinancials(prevFinancials => {
		return prevFinancials.map(item => {
			if (item.year === year) {
				const updatedData = { ...item };
				
				// Apply all calculated fields to the state
				Object.entries(calculatedFields).forEach(([key, value]) => {
					updatedData[key] = parseFloat(value);
				});

				return updatedData;
			}
			return item;
		});
	});
	
	// Save calculated fields to database (mark as AI-generated since they're auto-calculated)
	if (Object.keys(calculatedFields).length > 0 && !skipSave) {
		try {
			// Filter out invalid values before saving
			const validCalculatedFields: { [key: string]: string } = {};
			Object.entries(calculatedFields).forEach(([key, value]) => {
				const numValue = parseFloat(value);
				if (!isNaN(numValue) && isFinite(numValue)) {
					validCalculatedFields[key] = value;
				} else {
					console.warn(`Skipping invalid calculated value for ${key}:`, value);
				}
			});

			if (Object.keys(validCalculatedFields).length) {
				if (addressId) {
					await updatePropertyFinancials(addressId as string, validCalculatedFields, year, 'ai');
				} else if (selectedPortfolio) {
					await updatePortfolioFinancials(selectedPortfolio.id, validCalculatedFields, year, 'ai');
				}
				console.log('Auto-calculated fields saved:', validCalculatedFields);
			} else {
				console.warn('No valid calculated fields to save');
			}
		} catch (error) {
			console.error('Error saving auto-calculated fields:', error);
		}
	}
	
	setIsAutoCalculating(false);
}

// Batch input change handler for better performance
const handleBatchInputChange = (values: { year: number; key: string; value: string; source: 'human' | 'ai' | 'calculator' }[]) => {
	console.log('Batch updating values:', values);

	// Group values by year for efficient processing
	const valuesByYear = values.reduce((acc, item) => {
		if (!acc[item.year]) acc[item.year] = [];
		acc[item.year].push(item);
		return acc;
	}, {} as Record<number, typeof values>);

	// Update state for all years at once
	setFinancials(prevFinancials => {
		return prevFinancials.map(yearData => {
			const yearValues = valuesByYear[yearData.year];
			if (!yearValues) return yearData;

			const updatedData = { ...yearData };

			yearValues.forEach(({ key, value, source }) => {
				const numValue = parseFloat(value);

				// Enhanced validation
				if (isNaN(numValue) || !isFinite(numValue)) {
					console.warn(`Invalid value provided for ${key}:`, value);
					return;
				}

				let finalValue: number;

				// For calculator source, use value as-is (already calculated with inflation)
				if (source === 'calculator') {
					finalValue = numValue;
				} else if (source === 'ai') {
					finalValue = numValue;
				} else {
					// For human input, apply inflation adjustment for years > 1
					const baseValue = yearData.year === 1 ? numValue :
						parseFloat(getValue(1, key)) || numValue;
					finalValue = yearData.year === 1 ? numValue : getInflationAdjustedValue(baseValue, yearData.year);
				}

				// Apply negative sign for fields that should be negative
				if (shouldBeNegative(key)) {
					finalValue = -Math.abs(finalValue);
				}

				// Final validation
				if (isFinite(finalValue)) {
					updatedData[key] = finalValue;
				}
			});

			return updatedData;
		});
	});

	// Batch save all financial data to database
	const handleBatchSaveFinancials = async () => {
		try {
			const affectedYears = Object.keys(valuesByYear).map(Number);
			console.log('Batch saving financial data for years:', affectedYears);

			// Prepare batch save promises
			const savePromises: Promise<any>[] = [];

			affectedYears.forEach(year => {
				const yearValues = valuesByYear[year];

				// Prepare data for saving primary field values
				const saveData: { [key: string]: string } = {};
				yearValues.forEach(({ key, value }) => {
					const numValue = parseFloat(value);
					if (!isNaN(numValue) && isFinite(numValue)) {
						let finalValue = numValue;

						// Apply negative sign for fields that should be negative
						if (shouldBeNegative(key)) {
							finalValue = -Math.abs(finalValue);
						}

						saveData[key] = finalValue.toString();
					}
				});

				// Calculate and include Effective Gross Income if any EGI components were updated
				const egiComponents = ['rental_income', 'other_income', 'vacancy_loss', 'credit_loss'];
				const hasEgiComponent = yearValues.some(({ key }) => egiComponents.includes(key));

				if (hasEgiComponent) {
					// Get current year data from state (which has been updated)
					const currentYearData = financials.find(f => f.year === year);
					if (currentYearData) {
						// Calculate EGI using the updated values
						const gsi = parseFloat(saveData['rental_income'] || currentYearData['rental_income'] || '0');
						const otherIncome = parseFloat(saveData['other_income'] || currentYearData['other_income'] || '0');
						const vacancyLoss = parseFloat(saveData['vacancy_loss'] || currentYearData['vacancy_loss'] || '0');
						const creditLoss = parseFloat(saveData['credit_loss'] || currentYearData['credit_loss'] || '0');

						const effectiveGrossIncome = gsi + otherIncome + vacancyLoss + creditLoss;

						// Include EGI in save data
						if (isFinite(effectiveGrossIncome)) {
							saveData['effective_gross_income'] = effectiveGrossIncome.toString();
							console.log(`Calculated EGI for year ${year}: ${effectiveGrossIncome} (GSI: ${gsi}, Other: ${otherIncome}, Vacancy: ${vacancyLoss}, Credit: ${creditLoss})`);
						}
					}
				}
				console.log('Save data before batch save:', saveData);
				// Add save promise for this year
				if (Object.keys(saveData).length > 0) {
					console.log(`Preparing batch save for year ${year}:`, saveData);

					const firstSource = yearValues[0]?.source || 'calculator';
					const backendSource: 'human' | 'ai' = firstSource === 'calculator' ? 'ai' : firstSource === 'human' ? 'human' : 'ai';

					if (addressId) {
						savePromises.push(updatePropertyFinancials(addressId as string, saveData, year, backendSource));
					} else if (selectedPortfolio) {
						savePromises.push(updatePortfolioFinancials(selectedPortfolio.id, saveData, year, backendSource));
					}
				}
			});

			// Execute all saves in parallel
			if (savePromises.length > 0) {
				console.log(`Executing ${savePromises.length} batch save operations...`);
				const results = await Promise.all(savePromises);
				console.log('Batch save completed successfully:', results);

				// After successful batch save, recalculate derived fields for all years
				affectedYears.forEach(year => {
					setTimeout(() => {
						recalculateAndSaveDerivedFields(year);
					}, 50 * year); // Stagger the recalculations slightly
				});
			} else {
				console.warn('No data to batch save');
			}

		} catch (error) {
			console.error('Batch save failed:', error);
		}
	};

	// Execute batch save
	handleBatchSaveFinancials();
};

// Modify handleInputChange to include calculator source
const handleInputChange = async (year: number, key: string, value: string, source: 'human' | 'ai' | 'calculator' = 'human') => {
	const numValue = parseFloat(value);

	// Enhanced validation to prevent database errors
	if (isNaN(numValue) || !isFinite(numValue)) {
		console.warn(`Invalid value provided for ${key}:`, value);
		return;
	}

	let finalValue: number;

	// For AI calculations, use the value as-is since AI already calculated for the specific year
	// For human input, apply inflation adjustment for years > 1
	if (source === 'ai') {
		finalValue = numValue;
	} else if(source === 'calculator') {
		// Get base value (year 1 value) for inflation calculation
		const baseValue = year === 1 ? numValue :
			parseFloat(getValue(1, key)) || numValue;

		finalValue = year === 1 ? numValue : getInflationAdjustedValue(baseValue, year);
	} else {
		finalValue = numValue;
	}

	// Apply negative sign for fields that should be negative
	if (shouldBeNegative(key)) {
		finalValue = -Math.abs(finalValue);
	}

	// Final validation before processing
	if (!isFinite(finalValue)) {
		console.error(`Calculated value is not finite for ${key}:`, finalValue);
		return;
	}

	// Create a new array with updated values
	const updatedFinancials = financials.map(item => {
		if (item.year === year) {
			const newData = { ...item };
			newData[key] = finalValue;

			// Auto-calculate rental_income when long_term_rental or short_term_rental changes
			if ((key === 'long_term_rental' || key === 'short_term_rental') && source === 'human') {
				console.log(`${key} changed, auto-calculating rental_income for year ${year}`);

				// Get current values for both sub-items
				const currentLongTerm = key === 'long_term_rental' ? finalValue :
					parseFloat(newData['long_term_rental'] || '0');
				const currentShortTerm = key === 'short_term_rental' ? finalValue :
					parseFloat(newData['short_term_rental'] || '0');

				// Calculate new rental income
				const newRentalIncome = currentLongTerm + currentShortTerm;
				newData['rental_income'] = newRentalIncome;

				// Update metadata for rental_income
				newData.metadata = {
					...newData.metadata,
					rental_income: {
						source: 'calculator',
						timestamp: new Date().toISOString()
					}
				};

				console.log(`Auto-calculated rental_income for year ${year}: ${newRentalIncome} (LTR: ${currentLongTerm}, STR: ${currentShortTerm})`);
			}

			// Calculate effective gross income
			const gsi = parseFloat(newData['rental_income'] || '0');
			const otherIncome = parseFloat(newData['other_income'] || '0');
			const vacancyLoss = parseFloat(newData['vacancy_loss'] || '0');
			const creditLoss = parseFloat(newData['credit_loss'] || '0');

			const effectiveGrossIncome = gsi + otherIncome + vacancyLoss + creditLoss;
			newData['effective_gross_income'] = effectiveGrossIncome;
			return newData;
		}
		return item;
	});

	// Auto-recalculate dependent fields when Year 1 GSI is manually changed
	if (key === 'rental_income' && year === 1 && source === 'human') {

		const numValue = parseFloat(value);
		if (!isNaN(numValue) && numValue > 0) {
			const allYearsSaveData = await autoRecalculateYear1GSIDependentFields(numValue);
			handleBatchInputChange(allYearsSaveData);
		}
	} else {
		// Update state immediately
		setFinancials(updatedFinancials);
		// Prepare data for saving to database
		const saveData: { [key: string]: number } = {
			[key]: finalValue
		};

		// If rental_income was auto-calculated, include it in save data
		if ((key === 'long_term_rental' || key === 'short_term_rental') && source === 'human') {
			const updatedYear = updatedFinancials.find(item => item.year === year);
			if (updatedYear?.['rental_income'] !== undefined) {
				saveData['rental_income'] = updatedYear['rental_income'];
			}
		}

		// If effective gross income was updated, include it in the save data
		const updatedYear = updatedFinancials.find(item => item.year === year);
		if (updatedYear?.['effective_gross_income'] !== undefined) {
			saveData['effective_gross_income'] = updatedYear['effective_gross_income'];
		}

		// Save to database
		console.log(`Saving ${source} value for ${key} in year ${year}:`, finalValue);
		
		// Only use debounced save for human input to prevent server action conflicts with AI
		if (source === 'human') {
			console.log(`Human input detected for ${key}, calling handleSaveFinancials and marking for recalculation`);
			handleSaveFinancials(saveData, year, source).then(() => {
				console.log(`Database save completed for ${key} in year ${year}`);
				// Recalculate derived fields (refresh will happen via useEffect)
				recalculateAndSaveDerivedFields(year);

			}).catch((error) => {
				console.error(`Database save failed for ${key} in year ${year}:`, error);
			});
			
		} else {
			console.log(`AI/Calculator input detected for ${key}, calling recalculateAndSaveDerivedFields directly`);
			// For AI calculations, skip debounced saves to prevent conflicts
			recalculateAndSaveDerivedFields(year, true); // Skip save in derived fields
		}

		// If this is a sub-item that affects parent calculations or property_value that affects cap_rate, recalculate all years
		const subItemsAffectingParents = [
			'long_term_rental', 'short_term_rental', // affects rental_income
			'management_fees', 'leasing_fees', 'legal_fees', 'accounting_fees',
			'engineering_fees', 'marketing_fees', 'consulting_fees' // affects professional_fees
		];

		if (subItemsAffectingParents.includes(key)) {
			// Recalculate all years to ensure parent totals and dependent calculations are updated
			[1, 2, 3, 4, 5].forEach(async yearToRecalc => {
				if (yearToRecalc !== year) {
					await recalculateAndSaveDerivedFields(yearToRecalc);
				}
			});
		}
	}
};

	// Fetch inflation rate when component mounts
	useEffect(() => {
		const fetchInflationRate = async () => {
			try {
				console.log('Fetching current inflation rate from FRED API...');
				const response = await fetch('/api/fred-inflation');
				const data = await response.json();
				const rate = data.error ? 3.0 : data.rate;
				setInflationRate(rate);
				console.log('Inflation rate updated:', rate + '%', data.error ? '(fallback)' : '(from FRED)');
			} catch (error) {
				console.warn('Failed to fetch inflation rate:', error);
				setInflationRate(3.0);
				console.log('Using fallback inflation rate: 3.0%');
			}
		};

		fetchInflationRate();
	}, []);

	// Portfolio completion tracking
	useEffect(() => {
		if (selectedPortfolio && !addressId) {
			const fetchPortfolioCompletion = async () => {
				try {
					const response = await fetch(`/api/recalculate-financials?portfolioId=${selectedPortfolio.id}`);
					const data = await response.json();
					if (data.success) {
						setPortfolioCompletion(data.portfolioCompletion);
					}
				} catch (error) {
					console.error('Error fetching portfolio completion:', error);
				}
			};
			fetchPortfolioCompletion();
		}
	}, [selectedPortfolio, addressId, financials]);

	// Auto-recalculation when values change
	const triggerAutoRecalculation = async (changedField: string) => {
		if (!selectedPortfolio || isAutoRecalculating) return;
		
		setIsAutoRecalculating(true);
		try {
			const response = await fetch('/api/recalculate-financials', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					addressId: addressId || null,
					portfolioId: selectedPortfolio.id,
					triggerField: changedField
				})
			});

			const data = await response.json();
			if (data.success) {
				// Update portfolio completion
				setPortfolioCompletion(data.portfolioCompletion || 0);
				
				// Refresh financial data to show updated calculations without page refresh
				if (addressId) {
					const updatedFinancials = await getPropertyFinancialsDB(addressId);
					setFinancials(updatedFinancials);
				} else {
					const updatedFinancials = await getPortfolioFinancialsAggregated(selectedPortfolio.id);
					setFinancials(updatedFinancials as FinancialYear[]);
				}
			}
		} catch (error) {
			console.error('Error in auto-recalculation:', error);
		} finally {
			setIsAutoRecalculating(false);
		}
	};

	// Listen for financials recalculation events
	useEffect(() => {
		const handleFinancialsRecalculated = async (event: any) => {
			const { addressId: eventAddressId, portfolioId, data: calculatedData } = event.detail;
			
			// Only update if it's for the current view
			if ((addressId && eventAddressId === addressId) || (!addressId && portfolioId === selectedPortfolio?.id)) {
				// If we have calculated data from the API, apply it directly to the state
				if (calculatedData) {
					// Convert the calculated data format to the format expected by the UI
					const updatedFinancials = financials.map(yearData => {
						const newYearData = { ...yearData };
						
						// Apply calculated values for this year
						Object.entries(calculatedData).forEach(([fieldKey, yearValues]) => {
							if (yearValues && typeof yearValues === 'object') {
								const valueForYear = (yearValues as any)[yearData.year];
								if (valueForYear !== undefined && valueForYear !== null) {
									newYearData[fieldKey] = valueForYear;
								}
							}
						});
						
						return newYearData;
					});
					
					setFinancials(updatedFinancials);
				} else {
					// Fallback: refresh from database
					if (addressId) {
						const updatedFinancials = await getPropertyFinancialsDB(addressId);
						setFinancials(updatedFinancials);
					} else if (selectedPortfolio) {
						const updatedFinancials = await getPortfolioFinancialsAggregated(selectedPortfolio.id);
						setFinancials(updatedFinancials as FinancialYear[]);
					}
				}
				
				// Add smooth glare effect
				setTimeout(() => {
					addTableGlareEffect();
				}, 500);
			}
		};

		const handlePortfolioCompletionUpdate = (event: any) => {
			setPortfolioCompletion(event.detail.completion);
		};

		window.addEventListener('financialsRecalculated', handleFinancialsRecalculated);
		window.addEventListener('portfolioCompletionUpdate', handlePortfolioCompletionUpdate);

		return () => {
			window.removeEventListener('financialsRecalculated', handleFinancialsRecalculated);
			window.removeEventListener('portfolioCompletionUpdate', handlePortfolioCompletionUpdate);
			// Clear any ongoing expense calculation polling
			setExpenseCalculationTaskToken(null);
			setExpenseCalculationMessage('');
		};
	}, [addressId, selectedPortfolio]);

// useEffect for polling expense calculation status
useEffect(() => {
	if (!expenseCalculationTaskToken) return;

	let pollInterval: NodeJS.Timeout;
	let timeoutId: NodeJS.Timeout;
	const maxDuration = 10 * 60 * 1000; // 10 minutes safety timeout

	const pollStatus = async () => {
		try {
			console.log('Polling status for task:', expenseCalculationTaskToken);
			const statusResponse = await FinanceCalculationService.getCalculationStatus(expenseCalculationTaskToken);
			console.log('Status response received:', statusResponse);

			// Update message from API response
			if (statusResponse.message) {
				setExpenseCalculationMessage(statusResponse.message);
			}

			if (statusResponse.status === 'completed' && statusResponse.data) {
				console.log('Calculation completed successfully:', statusResponse);
				clearInterval(pollInterval);
				clearTimeout(timeoutId);
				setExpenseCalculationTaskToken(null);
				setExpenseCalculationMessage('');

				// Process the completed calculation
				await processExpenseProjectionsData(statusResponse);

			} else if (statusResponse.status === 'failed') {
				console.error('Calculation failed:', statusResponse.message);
				clearInterval(pollInterval);
				clearTimeout(timeoutId);
				setExpenseCalculationTaskToken(null);
				setExpenseCalculationMessage('');
				setIsCalculatingExpenses(false);

			} else if (statusResponse.status === 'processing' || statusResponse.status === 'pending') {
				console.log(`Calculation still ${statusResponse.status}, continuing to poll...`);
				// Continue polling - no timeout logic, rely on server status

			} else {
				console.error(`Unknown status: ${statusResponse.status}`);
				clearInterval(pollInterval);
				clearTimeout(timeoutId);
				setExpenseCalculationTaskToken(null);
				setExpenseCalculationMessage('');
				setIsCalculatingExpenses(false);
			}
		} catch (error) {
			console.error('Polling failed:', error);

			// Check if this is a permanent error that should stop polling
			const errorMessage = error instanceof Error ? error.message : String(error);

			// Stop polling for authentication errors or permanent failures
			if (errorMessage.includes('Unauthorized') ||
				errorMessage.includes('401') ||
				errorMessage.includes('403') ||
				errorMessage.includes('Task not found') ||
				errorMessage.includes('Invalid task token')) {
				console.error('Permanent error detected, stopping polling:', errorMessage);
				clearInterval(pollInterval);
				clearTimeout(timeoutId);
				setExpenseCalculationTaskToken(null);
				setExpenseCalculationMessage('Calculation failed due to authentication or invalid task');
				setIsCalculatingExpenses(false);
			} else {
				// For temporary errors (network, 500, etc.), continue polling but show error message
				console.warn('Temporary error, continuing to poll:', errorMessage);
				setExpenseCalculationMessage('Connection issue, retrying...');
			}
		}
	};

	// Set up safety timeout
	timeoutId = setTimeout(() => {
		console.warn('Calculation timed out after 10 minutes, stopping polling');
		clearInterval(pollInterval);
		setExpenseCalculationTaskToken(null);
		setExpenseCalculationMessage('Calculation timed out after 10 minutes');
		setIsCalculatingExpenses(false);
	}, maxDuration);

	// Start polling immediately, then every 10 seconds
	pollStatus();
	pollInterval = setInterval(pollStatus, 10000);

	// Cleanup function
	return () => {
		if (pollInterval) {
			clearInterval(pollInterval);
		}
		if (timeoutId) {
			clearTimeout(timeoutId);
		}
	};
}, [expenseCalculationTaskToken]);

// Process expense projections data (extracted from original function)
const processExpenseProjectionsData = async (response: FinanceCalculationResponse) => {
	console.log('Processing expense projections response:', response);

	// Handle nested data structure from polling response
	const responseData = response.data?.data || response.data;

	if (!response.success || !responseData || !selectedPortfolio) {
		console.error('Invalid response data for expense projections', {
			success: response.success,
			hasData: !!responseData,
			hasPortfolio: !!selectedPortfolio
		});
		setIsCalculatingExpenses(false);
		return;
	}

	try {
		// Create a single comprehensive state update
		const newFinancials = [...financials];
		const dbUpdatePromises: Promise<any>[] = [];

		// Process all years and prepare updates
		for (const [year, yearData] of Object.entries(responseData)) {
			console.log(`Processing year ${year} data:`, yearData);

			const yearInt = parseInt(year);
			const yearIndex = newFinancials.findIndex(f => f.year === yearInt);

			if (yearIndex >= 0) {
				const updateData: { [key: string]: string } = {};

				// Expense fields to update
				const expenseFields = [
					'property_tax', 'insurance', 'repairs', 'maintenance',
					'professional_fees', 'utilities', 'services', 'reserves',
					'management_fees', 'leasing_fees', 'legal_fees', 'accounting_fees',
					'engineering_fees', 'marketing_fees', 'consulting_fees'
				];

				// Process each field
				Object.entries(yearData as any).forEach(([key, value]) => {
					if (expenseFields.includes(key) && value !== null && value !== undefined) {
						// Handle both numeric values and string values
						let numValue: number;

						if (typeof value === 'number') {
							numValue = value;
						} else {
							const stringValue = String(value).trim();
							if (stringValue === '') {
								// Skip empty strings - they don't represent calculated values
								return;
							}
							numValue = parseFloat(stringValue);
						}

						if (!isNaN(numValue) && isFinite(numValue)) {
							// Update local state immediately
							newFinancials[yearIndex][key] = numValue;
							// Prepare for database save
							updateData[key] = numValue.toString();
							console.log(`✓ Updated ${key} for year ${year}: ${numValue}`);
						} else {
							console.warn(`✗ Invalid value for ${key}:`, value);
						}
					}
				});

				// Save to database (async, don't wait)
				if (Object.keys(updateData).length > 0) {
					console.log(`Saving to database for year ${year}:`, updateData);
					const savePromise = (async () => {
						try {
							if (addressId) {
								await updatePropertyFinancials(addressId, updateData, yearInt, 'ai');
							} else {
								await updatePortfolioFinancials(selectedPortfolio.id, updateData, yearInt, 'ai');
							}
							console.log(`✓ Database saved for year ${year}`);
						} catch (error) {
							console.error(`✗ Database save failed for year ${year}:`, error);
							// Don't throw - continue with UI updates
						}
					})();
					dbUpdatePromises.push(savePromise);
				}
			}
		}

		// Update state ONCE with all changes
		console.log('=== UPDATING STATE WITH NEW FINANCIALS ===');
		setFinancials(newFinancials);

		// Wait for all database saves to complete BEFORE recalculation
		Promise.all(dbUpdatePromises).then(() => {
			console.log('✓ All database saves completed');

			// Add a small delay to ensure database writes are committed
			return new Promise(resolve => setTimeout(resolve, 200));
		}).then(() => {
			// Now that all expense data is saved, recalculate derived fields
			console.log('Starting recalculation for derived fields...');
			const params = {
				prop_address_id: addressId || '',
				portfolio_id: selectedPortfolio.id,
				return_projections: true,
				override_user_input: false,
				year: 1,
				params: {
					default_source: "user_provided",
					skip_validation: true,
					force_completion: true
				}
			};
			return FinanceCalculationService.recalculate(params);
		}).then((recalcResponse) => {
			console.log('Recalculation response:', recalcResponse);

			if (recalcResponse.success && recalcResponse.data) {
				// Apply derived field updates
				setFinancials(currentFinancials => {
					const derivedFinancials = [...currentFinancials];

					for (const [year, yearData] of Object.entries(recalcResponse.data || {})) {
						const yearIndex = derivedFinancials.findIndex(f => f.year === parseInt(year));
						if (yearIndex >= 0) {
							Object.entries(yearData as any).forEach(([key, value]) => {
								if (value !== null && value !== undefined) {
									const numValue = typeof value === 'number' ? value : parseFloat(String(value));
									if (!isNaN(numValue) && isFinite(numValue)) {
										derivedFinancials[yearIndex][key] = numValue;
										console.log(`✓ Updated derived field ${key} in year ${year}: ${numValue}`);
									}
								}
							});
						}
					}

					return derivedFinancials;
				});
				console.log('=== EXPENSE PROJECTIONS COMPLETED SUCCESSFULLY ===');
			}
		}).catch((error) => {
			console.error('Recalculation failed (non-fatal):', error);
		}).finally(() => {
			setIsCalculatingExpenses(false);
			console.log('=== EXPENSE PROJECTIONS FUNCTION COMPLETED ===');
		});

	} catch (error) {
		console.error('Critical error in processing expense projections:', error);
		setIsCalculatingExpenses(false);
	}
};

const handleExpenseProjectionsRecalculate = async (e?: React.MouseEvent) => {
	// Prevent any form submission or default behavior
	if (e) {
		e.preventDefault();
		e.stopPropagation();
	}

	if (!selectedPortfolio || isCalculatingExpenses) return;

	setIsCalculatingExpenses(true);
	setExpenseCalculationMessage('Initializing expense projections calculation...');
	console.log('=== STARTING EXPENSE PROJECTIONS RECALCULATION ===');

	try {
		const params = {
			prop_address_id: addressId || '',
			portfolio_id: selectedPortfolio.id,
			return_projections: true,
			override_user_input: false,
			year: 1,
			params: {
				default_source: "user_provided",
				skip_validation: true,
				force_completion: true
			}
		};

		console.log('Starting expense projections calculation with params:', params);
		const initialResponse = await FinanceCalculationService.calculateExpenseProjections(params);
		console.log('Initial expense projections response:', initialResponse);

		// Check if we got a task token (async calculation) or immediate data
		if (initialResponse.task_token) {
			console.log('Got task token, starting background polling:', initialResponse.task_token);
			// Set the task token to trigger useEffect polling
			setExpenseCalculationTaskToken(initialResponse.task_token);
		} else if (initialResponse.data) {
			console.log('Got immediate response with data');
			// Process immediately for synchronous response
			await processExpenseProjectionsData(initialResponse);
		} else {
			throw new Error('No task token or data received from calculation service');
		}

	} catch (error) {
		console.error('Critical error in expense projections:', error);
		setIsCalculatingExpenses(false);
	}
}

// Handle refresh of aggregated portfolio financials
const handleRefreshPortfolioAggregation = async () => {
	if (!selectedPortfolio || isRefreshingAggregation) return;
	
	setIsRefreshingAggregation(true);
	try {
		await refreshPortfolioFinancials(selectedPortfolio.id);
		
		// Reload the aggregated data
		const refreshedData = await getPortfolioFinancialsAggregated(selectedPortfolio.id);
		setFinancials(refreshedData as FinancialYear[]);
		
	} catch (error) {
		console.error('Error refreshing portfolio aggregation:', error);
	} finally {
		setIsRefreshingAggregation(false);
	}
};

// Handle any accidental form submissions
const handleFormSubmit = (e: React.FormEvent) => {
	e.preventDefault();
	e.stopPropagation();
	console.warn('Form submission prevented');
	return false;
};

if (!isClient) {
	return (
		<div className="flex flex-col items-center justify-center py-10">
			<Spinner size="lg" text="Loading..." />
		</div>
	);
}

if (isLoadingFinancials) {
	return (
		<div className="flex flex-col items-center justify-center py-10">
			<Spinner size="lg" text="Loading financials..." />
		</div>
	)
}

// Source Legend Component
const SourceLegend = () => (
	<div className="flex items-center space-x-4 text-xs text-gray-600 mb-2 px-4">
		<span className="text-gray-700 font-medium mr-2">Edit Sources:</span>
		<div className="flex items-center">
			<div className="w-4 h-3 border-l-4 border-yellow-400 bg-yellow-50 mr-1 rounded-sm"></div>
			<span>Manual</span>
		</div>
		<div className="flex items-center">
			<div className="w-4 h-3 border-l-4 border-gray-200 bg-gray-50 mr-1 rounded-sm"></div>
			<span>AI/Calculator</span>
		</div>
	</div>
);

return(
	<div
		className={`relative h-full flex flex-col ${isFocused ? 'fixed inset-0 bg-white z-50 pt-24 px-4 pb-4' : ''}`}
		onSubmit={handleFormSubmit}
	>
		{/* Header - Always visible */}
		<div className="flex justify-between items-start m-4">
			<div className="flex-1">
				<WorkspaceDetailFinancialsHeader />
			</div>
			<div className="flex items-center space-x-2 ml-4">
				{/* Portfolio refresh button - only show for portfolio view */}
				{!addressId && selectedPortfolio && (
					<button
						onClick={handleRefreshPortfolioAggregation}
						disabled={isRefreshingAggregation}
						className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
						title="Refresh portfolio financials by aggregating all property data"
					>
						{isRefreshingAggregation ? (
							<Spinner size="sm" />
						) : (
							<FontAwesomeIcon icon={faSync} className="h-4 w-4 mr-2" />
						)}
						{isRefreshingAggregation ? 'Refreshing...' : 'Refresh'}
					</button>
				)}
				
				{/* Property-specific buttons - only show for property view */}
				{addressId && (
					<>
						<WorkspaceDetailFinancialsExportPDF
							propertyImage={propertyInfo.image}
							propertyName={propertyInfo.name}
							propertyAddress={propertyInfo.address}
							financials={financials}
							sections={sections}
						/>
						<button
							onClick={handleFocusToggle}
							className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
						>
							<FontAwesomeIcon 
								icon={isFocused ? faCompress : faExpand} 
								className="h-4 w-4 mr-2" 
							/>
							{isFocused ? 'Exit Fullscreen' : 'Fullscreen'}
						</button>
					</>
				)}
			</div>
		</div>

		{/* Source Legend */}
		<SourceLegend />

		{/* Auto-calculation notification */}
		{/* {isAutoCalculating && (
			<div className="mx-2 mb-4 px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg flex items-center text-sm text-blue-700">
				<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
				Auto-calculating totals and derived metrics...
			</div>
		)} */}

		{/* Portfolio aggregation info banner */}
		{!addressId && selectedPortfolio && (
			<div className="mx-2 mb-4 px-4 py-3 bg-indigo-50 border border-indigo-200 rounded-lg flex items-center text-sm text-indigo-700">
				<svg className="w-5 h-5 mr-3 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
					<path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
				</svg>
				<div className="flex-1">
					<p className="font-medium">Portfolio-Wide Financial Summary</p>
					<p className="text-xs text-indigo-600 mt-1">
						These financials represent aggregated data from all {properties?.length || 0} properties in your portfolio. 
						Values are automatically summed across all properties to provide a comprehensive portfolio view.
					</p>
				</div>
			</div>
		)}

		{/* Portfolio completion progress bar */}
		{!addressId && selectedPortfolio && (
			<div className="mx-2 mb-4 px-4 py-3 bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg">
				<div className="flex items-center justify-between mb-2">
					<h4 className="text-sm font-medium text-gray-900">Portfolio Financial Completion</h4>
					<span className="text-sm font-semibold text-gray-700">{portfolioCompletion}%</span>
				</div>
				<div className="w-full bg-gray-200 rounded-full h-3 mb-2">
					<div 
						className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500 ease-out"
						style={{ width: `${portfolioCompletion}%` }}
					></div>
				</div>
				<p className="text-xs text-gray-600">
					{portfolioCompletion < 30 ? 'Getting started - Fill in basic income and expense data' :
					 portfolioCompletion < 70 ? 'Good progress - Continue adding detailed financial information' :
					 portfolioCompletion < 95 ? 'Almost complete - Just a few more fields to fill' :
					 'Excellent! Your portfolio financials are comprehensive'}
				</p>
				{isAutoCalculating && (
					<div className="mt-2 flex items-center text-xs text-blue-600">
						<div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-2"></div>
						Auto-updating calculations...
					</div>
				)}
			</div>
		)}
		
		{/* Scrollable content area */}
		<div className={`flex-1 overflow-y-auto space-y-8 pr-2 rounded-[15px] border-inset ${isFocused ? 'px-4' : ''}`}>
			{sections.map((section, sectionIndex) => (
				<div key={`section-${sectionIndex}-${section.title}`} className="bg-white rounded-2xl shadow-sm border border-gray-200 relative">
					{/* Blur overlay for Expense Projections section */}
					{section.title === "Expense Projections" && isCalculatingExpenses && (
						<div className="absolute inset-0 bg-white bg-opacity-80 backdrop-blur-sm rounded-2xl z-10 flex flex-col items-center justify-center">
							<div className="bg-white rounded-lg shadow-xl border border-gray-200 p-6 max-w-sm mx-auto">
								<div className="flex flex-col items-center space-y-4">
									{/* Progress bar */}
									<div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
										<div className="bg-gradient-to-r from-blue-500 to-blue-600 h-full rounded-full animate-pulse transition-all duration-1000 ease-in-out"
											 style={{ width: '65%' }}>
											<div className="h-full bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
										</div>
									</div>

									{/* Spinner and text */}
									<div className="flex items-center space-x-3">
										<div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-300 border-t-blue-600"></div>
										<span className="text-sm font-semibold text-gray-900">Calculating Expense Projections</span>
									</div>

									{/* Message */}
									<p className="text-xs text-gray-600 text-center leading-relaxed">
										{expenseCalculationMessage || 'AI is analyzing your property data and market conditions to generate accurate expense projections...'}
									</p>

								</div>
							</div>
						</div>
					)}

					<div className="overflow-x-auto">
						<table className="min-w-full financial-table">
							<thead>
								<tr className="bg-gray-50">
									<th scope="col" className="py-2 pl-6 pr-3 text-left text-sm font-semibold text-gray-900 rounded-tl-2xl">
										<div className="flex items-center justify-between">
											<span>{section.title}</span>
											{/* AI Recalc button for Expense Projections - only show for property view */}
											{section.title === "Expense Projections" && addressId && (
												<button
													type="button"
													onClick={handleExpenseProjectionsRecalculate}
													disabled={isCalculatingExpenses}
													className="ml-2 inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
													title="AI Recalculate expense projections"
												>
													{isCalculatingExpenses ? (
														<Spinner size="sm" />
													) : (
														<FontAwesomeIcon icon={faHexagonNodes} className="h-3 w-3 mr-1" />
													)}
													{isCalculatingExpenses ? 'Calculating...' : 'AI Recalc'}
												</button>
											)}
										</div>
									</th>
									<th scope="col" className="px-6 py-2 text-left text-sm font-semibold text-gray-900">Year 1</th>
									<th scope="col" className="px-6 py-2 text-left text-sm font-semibold text-gray-900">Year 2</th>
									<th scope="col" className="px-6 py-2 text-left text-sm font-semibold text-gray-900">Year 3</th>
									<th scope="col" className="px-6 py-2 text-left text-sm font-semibold text-gray-900">Year 4</th>
									<th scope="col" className="px-6 py-2 text-left text-sm font-semibold text-gray-900 rounded-tr-2xl">Year 5</th>
								</tr>
							</thead>
							<tbody className="divide-y divide-gray-200 bg-white">
								{section.items.map((item, itemIndex) => (
									<React.Fragment key={`item-${sectionIndex}-${itemIndex}-${item.key}`}>
										{/* Main item row */}
										<tr 
											className={`${item.isSectionTotal ? 'bg-gray-50' : ''} ${item.expandable ? 'cursor-pointer hover:bg-gray-50' : ''}`}
											onClick={item.expandable ? () => toggleExpanded(item.key) : undefined}
										>
											<td className={`w-1/4  pl-6 pr-3 text-sm font-medium text-gray-900 ${itemIndex === section.items.length - 1 && !expandedRows.has(item.key) ? 'rounded-bl-2xl' : ''}`}>
												<div className="flex items-center">
													{item.expandable && (
														<svg 
															className={`w-4 h-4 mr-2 transition-transform ${expandedRows.has(item.key) ? 'rotate-90' : ''}`}
															fill="none" 
															stroke="currentColor" 
															viewBox="0 0 24 24"
														>
															<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
														</svg>
													)}
													<span>{item.label}</span>
													{financialTooltips[item.key] && (
														<Tooltip
															title={item.label}
															definition={financialTooltips[item.key].definition}
															calculation={financialTooltips[item.key].calculation}
															aiAssistance={financialTooltips[item.key].aiAssistance}
														/>
													)}
												</div>
											</td>
											{[1, 2, 3, 4, 5].map((year) => (
												<td key={`year-${year}-${item.key}`} className={`w-1/8 px-3  text-sm text-gray-500 ${itemIndex === section.items.length - 1 && year === 5 && !expandedRows.has(item.key) ? 'rounded-br-2xl' : ''}`}>
													<WorkspaceDetailFinancialsItem
														key={`${item.key}-${year}`}
														section={section}
														item={item}
														year={year}
														getValue={getValue}
														handleInputChange={handleInputChange}
														handleBatchInputChange={handleBatchInputChange}
														calculateSectionTotal={calculateSectionTotal}
														calculateNOI={calculateNOI}
														units={units}
														setUnits={setUnits}
														metadata={getMetadataForYear(year)}
														disabled={isAutoCalculating}
													/>
												</td>
											))}
										</tr>
										
										{/* Sub-items (expanded) */}
										{item.expandable && item.subItems && expandedRows.has(item.key) && 
											item.subItems.map((subItem, subIndex) => (
												<tr key={`subitem-${sectionIndex}-${itemIndex}-${subIndex}-${subItem.key}`} className="bg-gray-25">
													<td className={`w-1/4 py-1.5 pl-12 pr-3 text-sm text-gray-700 ${itemIndex === section.items.length - 1 && subIndex === item.subItems!.length - 1 ? 'rounded-bl-2xl' : ''}`}>
														<div className="flex items-center">
															<span>{subItem.label}</span>
															{financialTooltips[subItem.key] && (
																<Tooltip
																	title={subItem.label}
																	definition={financialTooltips[subItem.key].definition}
																	calculation={financialTooltips[subItem.key].calculation}
																	aiAssistance={financialTooltips[subItem.key].aiAssistance}
																/>
															)}
														</div>
													</td>
													{[1, 2, 3, 4, 5].map((year) => (
														<td key={`year-${year}-${subItem.key}`} className={`w-1/8 px-3 py-1.5 text-sm text-gray-500 ${itemIndex === section.items.length - 1 && subIndex === item.subItems!.length - 1 && year === 5 ? 'rounded-br-2xl' : ''}`}>
															<WorkspaceDetailFinancialsItem
																key={`${subItem.key}-${year}`}
																section={section}
																item={subItem}
																year={year}
																getValue={getValue}
																handleInputChange={handleInputChange}
																handleBatchInputChange={handleBatchInputChange}
																calculateSectionTotal={calculateSectionTotal}
																calculateNOI={calculateNOI}
																units={units}
																setUnits={setUnits}
																metadata={getMetadataForYear(year)}
																disabled={isAutoCalculating}
															/>
														</td>
													))}
												</tr>
											))
										}
									</React.Fragment>
								))}
							</tbody>
						</table>
					</div>
				</div>
			))}
		</div>

		{/* Overlay with loading spinner during recalculation */}
		{isAutoCalculating && (
			<div className="absolute inset-0 backdrop-blur-sm bg-white/30 z-50">
				<div className="sticky top-4 flex justify-center pt-4">
					<div className="mx-2 px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg flex items-center text-sm text-blue-700 shadow-lg">
						<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
						Auto-calculating totals and derived metrics...
					</div>
				</div>
			</div>
		)}
	</div>
)
}