'use client';

import React from 'react';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPercent, faBuilding, faCalculator, faMinus, faPlus } from "@fortawesome/free-solid-svg-icons";

interface YearValue {
    year: number;
    key: string;
    value: string;
    source: 'human' | 'ai' | 'calculator';
}

interface Props {
    units: { [key: string]: any }[];
    setUnits: React.Dispatch<React.SetStateAction<{ [key: string]: any }[]>>;
    setIsCalculatorVisible: React.Dispatch<React.SetStateAction<boolean>>;
    handleSetCalculatorValue: (value: number) => void;
    handleInputChange: (year: number, key: string, value: string, source?: 'human' | 'ai' | 'calculator') => void;
    handleBatchInputChange?: (values: YearValue[]) => void;
    year?: number;
    itemKey?: string;
    grossScheduledIncome?: number;
    getValue?: (year: number, key: string) => string;
}

export default function Calculator({
    units,
    setUnits,
    setIsCalculatorVisible,
    handleSetCalculatorValue,
    handleInputChange,
    handleBatchInputChange,
    year = 1,
    itemKey,
    grossScheduledIncome = 0,
    getValue
}: Props) {
    const [tab, setTab] = React.useState<'info' | 'calculator' | 'percentage'>('info');
    const [monthlyValue, setMonthlyValue] = React.useState(0);
    const [numberOfMonths, setNumberOfMonths] = React.useState(12);
    const [percentageValue, setPercentageValue] = React.useState(() => {
        // Set default industry-standard percentages
        switch (itemKey) {
            case 'other_income':
                return 3.0; // 3% of GSI for other income
            case 'vacancy_loss':
                return 5.0; // 5% vacancy rate
            case 'credit_loss':
                return 2.0; // 2% credit loss rate
            default:
                return 0;
        }
    });
    const [inflationRate, setInflationRate] = React.useState(3.0);
    const [isLoadingInflation, setIsLoadingInflation] = React.useState(false);

    React.useEffect(() => {
        if (year > 1) {
            setIsLoadingInflation(true);
            fetch('/api/fred-inflation')
                .then(res => res.json())
                .then(data => {
                    setInflationRate(data.error ? 3.0 : data.rate);
                })
                .catch(() => setInflationRate(3.0))
                .finally(() => setIsLoadingInflation(false));
        }
    }, [year]);

    React.useEffect(() => {
        if (itemKey && ['other_income', 'vacancy_loss', 'credit_loss'].includes(itemKey)) {
            setTab('percentage');
        } else if (itemKey === 'rental_income') {
            setTab('info');
        } else {
            setTab('calculator');
        }
    }, [itemKey]);

    const getInflationAdjustedValue = (baseValue: number) => {
        if (year > 1) {
            const rate = inflationRate / 100;
            const compound = Math.pow(1 + rate, year - 1);
            return Math.round(baseValue * compound);
        }
        return Math.round(baseValue);
    };

    const handleApply = () => {
        let value = 0;
        
        if (tab === 'info' && itemKey === 'rental_income') {
            const monthlyTotal = units.reduce((sum, unit) => {
                const baseRent = Math.round(parseFloat(unit.rent || '0'));
                return sum + getInflationAdjustedValue(baseRent);
            }, 0);
            value = monthlyTotal * 12;
            handleSetCalculatorValue(value);
            // For rental_income calculator, update long_term_rental which will auto-calculate rental_income
            handleInputChange(year, 'long_term_rental', value.toString(), 'calculator');
        } else if (tab === 'percentage') {
            const adjustedGSI = getInflationAdjustedValue(grossScheduledIncome);
            value = Math.round((percentageValue / 100) * adjustedGSI);
            if (itemKey === 'vacancy_loss' || itemKey === 'credit_loss') {
                value = -Math.abs(value);
            }
            handleSetCalculatorValue(value);
            handleInputChange(year, itemKey || '', value.toString(), 'calculator');
        }
        
        setIsCalculatorVisible(false);
    };

    // Helper function to get Year 1 GSI for percentage calculations
    const getYear1GSI = (): number => {
        if (!getValue) return grossScheduledIncome;

        const year1GSI = parseFloat(getValue(1, 'rental_income')) || 0;
        return year1GSI > 0 ? year1GSI : grossScheduledIncome;
    };

    // Helper function to get inflation-adjusted value for a specific year
    const getInflationAdjustedValueForYear = (baseValue: number, targetYear: number): number => {
        if (targetYear <= 1) return Math.round(baseValue);

        const rate = inflationRate / 100;
        const compound = Math.pow(1 + rate, targetYear - 1);
        return Math.round(baseValue * compound);
    };

    const handleApplytoAllYears = () => {
        if (!itemKey) {
            console.warn('No itemKey provided for apply to all years');
            return;
        }

        let baseValue = 0;

        try {
            // Calculate base value based on current calculator tab and settings
            if (tab === 'info' && itemKey === 'rental_income') {
                // For rental income, use existing Year 1 value as base, or calculate from units if no Year 1 value exists
                if (getValue) {
                    const existingYear1Value = parseFloat(getValue(1, 'rental_income')) || 0;
                    if (existingYear1Value > 0) {
                        baseValue = existingYear1Value;
                        console.log('Using existing Year 1 rental income as base:', baseValue);
                    } else {
                        // Fallback: calculate from units if no existing value
                        baseValue = units.reduce((sum, unit) => {
                            return sum + Math.round(parseFloat(unit.rent || '0'));
                        }, 0) * 12;
                        console.log('Calculated base value from units:', baseValue);
                    }
                } else {
                    // Fallback: calculate from units if getValue not available
                    baseValue = units.reduce((sum, unit) => {
                        return sum + Math.round(parseFloat(unit.rent || '0'));
                    }, 0) * 12;
                    console.log('Calculated base value from units (no getValue):', baseValue);
                }
            } else if (tab === 'percentage') {
                // Calculate percentage-based value using Year 1 GSI
                const year1GSI = getYear1GSI();
                baseValue = Math.round((percentageValue / 100) * year1GSI);

                // Apply negative for loss fields
                if (itemKey === 'vacancy_loss' || itemKey === 'credit_loss') {
                    baseValue = -Math.abs(baseValue);
                }
            } else {
                console.warn('Unsupported calculator tab for apply to all years:', tab);
                return;
            }

            // Validate base value
            if (!isFinite(baseValue) || isNaN(baseValue)) {
                console.error('Invalid base value calculated:', baseValue);
                return;
            }

            // Check if we have the batch handler, use it for better performance
            if (handleBatchInputChange) {
                // Prepare batch values for all years (1-5)
                const batchValues: YearValue[] = [];

                for (let targetYear = 1; targetYear <= 5; targetYear++) {
                    let adjustedValue = baseValue;

                    // For percentage-based calculations, recalculate based on each year's GSI
                    if (tab === 'percentage' && getValue) {
                        const yearGSI = parseFloat(getValue(targetYear, 'rental_income')) || 0;
                        const targetGSI = yearGSI > 0 ? yearGSI : getInflationAdjustedValueForYear(getYear1GSI(), targetYear);

                        adjustedValue = Math.round((percentageValue / 100) * targetGSI);

                        // Apply negative for loss fields
                        if (itemKey === 'vacancy_loss' || itemKey === 'credit_loss') {
                            adjustedValue = -Math.abs(adjustedValue);
                        }
                    } else {
                        // For rental income, apply inflation adjustment for years > 1
                        adjustedValue = getInflationAdjustedValueForYear(baseValue, targetYear);
                    }

                    // Validate adjusted value
                    if (!isFinite(adjustedValue) || isNaN(adjustedValue)) {
                        console.error(`Invalid adjusted value for year ${targetYear}:`, adjustedValue);
                        continue;
                    }

                    // Add values to batch
                    if (itemKey === 'rental_income') {
                        // For rental income, update both rental_income and long_term_rental
                        batchValues.push({
                            year: targetYear,
                            key: 'rental_income',
                            value: adjustedValue.toString(),
                            source: 'calculator'
                        });
                        batchValues.push({
                            year: targetYear,
                            key: 'long_term_rental',
                            value: adjustedValue.toString(),
                            source: 'calculator'
                        });
                    } else {
                        // For other fields, update the specific field
                        batchValues.push({
                            year: targetYear,
                            key: itemKey,
                            value: adjustedValue.toString(),
                            source: 'calculator'
                        });
                    }
                }

                // Apply all values in a single batch
                if (batchValues.length > 0) {
                    handleBatchInputChange(batchValues);
                    console.log(`Successfully applied ${itemKey} values to all years via batch update`);
                }
            } else {
                // Fallback to individual calls if batch handler not available
                for (let targetYear = 1; targetYear <= 5; targetYear++) {
                    let adjustedValue = baseValue;

                    if (tab === 'percentage' && getValue) {
                        const yearGSI = parseFloat(getValue(targetYear, 'rental_income')) || 0;
                        const targetGSI = yearGSI > 0 ? yearGSI : getInflationAdjustedValueForYear(getYear1GSI(), targetYear);
                        adjustedValue = Math.round((percentageValue / 100) * targetGSI);
                        if (itemKey === 'vacancy_loss' || itemKey === 'credit_loss') {
                            adjustedValue = -Math.abs(adjustedValue);
                        }
                    } else {
                        adjustedValue = getInflationAdjustedValueForYear(baseValue, targetYear);
                    }

                    if (!isFinite(adjustedValue) || isNaN(adjustedValue)) continue;

                    if (itemKey === 'rental_income') {
                        handleInputChange(targetYear, 'rental_income', adjustedValue.toString(), 'calculator');
                        handleInputChange(targetYear, 'long_term_rental', adjustedValue.toString(), 'calculator');
                    } else {
                        handleInputChange(targetYear, itemKey, adjustedValue.toString(), 'calculator');
                    }
                }
                console.log(`Successfully applied ${itemKey} values to all years via individual calls`);
            }

            // Close the calculator
            setIsCalculatorVisible(false);

        } catch (error) {
            console.error('Error applying values to all years:', error);
        }
    };

    const showPercentageTab = itemKey && ['other_income', 'vacancy_loss', 'credit_loss'].includes(itemKey);
    const showInfoTab = itemKey === 'rental_income';

    return (
        <div className="fixed top-1/4 left-1/2 transform -translate-x-1/2 border border-gray-300 rounded-md bg-white shadow-lg z-[9999] p-4 w-[480px]">
            <div className="flex border-b border-gray-300 mb-4">
                {showInfoTab && (
                    <button 
                        type="button"
                        onClick={() => setTab('info')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 -mb-[2px] ${
                            tab === 'info' 
                                ? 'border-blue-500 text-blue-600' 
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        <FontAwesomeIcon icon={faBuilding} className="mr-2" />
                        Units
                    </button>
                )}
                {!showPercentageTab && (
                    <button 
                        type="button"
                        onClick={() => setTab('calculator')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 -mb-[2px] ${
                            tab === 'calculator' 
                                ? 'border-blue-500 text-blue-600' 
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        <FontAwesomeIcon icon={faCalculator} className="mr-2" />
                        Calculator
                    </button>
                )}
                {showPercentageTab && (
                    <button 
                        type="button"
                        onClick={() => setTab('percentage')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 -mb-[2px] ${
                            tab === 'percentage' 
                                ? 'border-blue-500 text-blue-600' 
                                : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                    >
                        <FontAwesomeIcon icon={faPercent} className="mr-2" />
                        Percentage
                    </button>
                )}
            </div>

            <div className="mb-4 h-[300px]">
                {tab === 'info' && showInfoTab && (
                    <div className="h-[300px] overflow-y-auto pr-2">
                        {year > 1 && (
                            <div className="mb-4 p-3 bg-blue-50 rounded-md text-sm text-blue-700">
                                {isLoadingInflation ? (
                                    <div className="flex items-center">
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                        Loading inflation rate...
                                    </div>
                                ) : (
                                    `Inflation Rate: ${inflationRate.toFixed(1)}%`
                                )}
                            </div>
                        )}
                        <table className="w-full text-sm">
                            <thead>
                                <tr className="border-b">
                                    <th className="py-2 text-left">Unit</th>
                                    <th className="py-2 text-right">Monthly</th>
                                    {year > 1 && <th className="py-2 text-right">Adjusted</th>}
                                    <th className="py-2 text-right">Annual</th>
                                </tr>
                            </thead>
                            <tbody>
                                {units.map(unit => {
                                    const baseRent = Math.round(parseFloat(unit.rent || '0'));
                                    const adjustedRent = getInflationAdjustedValue(baseRent);
                                    return (
                                        <tr key={unit.id} className="border-b">
                                            <td className="py-2">{unit.unit}</td>
                                            <td className="py-2 text-right">${baseRent.toLocaleString()}</td>
                                            {year > 1 && (
                                                <td className="py-2 text-right text-blue-600">
                                                    ${adjustedRent.toLocaleString()}
                                                </td>
                                            )}
                                            <td className="py-2 text-right">
                                                ${(adjustedRent * 12).toLocaleString()}
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                )}

                {tab === 'calculator' && (
                    <div>
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Monthly Amount
                            </label>
                            <input
                                type="number"
                                value={monthlyValue || ''}
                                onChange={e => setMonthlyValue(Math.max(0, Number(e.target.value)))}
                                className="w-full p-2 border rounded"
                            />
                        </div>
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Number of Months
                            </label>
                            <div className="flex items-center space-x-2">
                                <button
                                    type="button"
                                    onClick={() => setNumberOfMonths(m => Math.max(1, m - 1))}
                                    className="p-1 border rounded hover:bg-gray-100"
                                >
                                    <FontAwesomeIcon icon={faMinus} />
                                </button>
                                <span className="text-lg font-medium w-8 text-center">
                                    {numberOfMonths}
                                </span>
                                <button
                                    type="button"
                                    onClick={() => setNumberOfMonths(m => Math.min(12, m + 1))}
                                    className="p-1 border rounded hover:bg-gray-100"
                                >
                                    <FontAwesomeIcon icon={faPlus} />
                                </button>
                            </div>
                        </div>
                        <div className="p-3 bg-gray-50 rounded-md">
                            <div className="text-sm text-gray-600">Annual Amount</div>
                            <div className="text-2xl font-bold">
                                ${getInflationAdjustedValue(monthlyValue * numberOfMonths).toLocaleString()}
                            </div>
                        </div>
                    </div>
                )}

                {tab === 'percentage' && showPercentageTab && (
                    <div>
                        <div className="mb-4 p-3 bg-blue-50 rounded-md">
                            <div className="text-sm text-blue-600">
                                GSI: ${grossScheduledIncome.toLocaleString()}
                            </div>

                        </div>
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Percentage
                            </label>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="number"
                                    value={percentageValue || ''}
                                    onChange={e => setPercentageValue(Math.max(0, Math.min(100, Number(e.target.value))))}
                                    className="flex-1 p-2 border rounded"
                                    step="0.1"
                                    min="0"
                                    max="100"
                                />
                                <span className="text-gray-600">%</span>
                            </div>
                        </div>
                        <div className="p-3 bg-gray-50 rounded-md">
                            <div className="text-sm text-gray-600">Calculated Amount</div>
                            <div className="text-2xl font-bold">
                                ${Math.abs(Math.round((percentageValue / 100) * grossScheduledIncome)).toLocaleString()}
                                {(itemKey === 'vacancy_loss' || itemKey === 'credit_loss') && ' (negative)'}
                            </div>
                        </div>
                    </div>
                )}
            </div>

            <div className="flex justify-end space-x-3 pt-3 border-t">
                <button
                    type="button"
                    onClick={() => setIsCalculatorVisible(false)}
                    className="px-4 py-2 text-sm border rounded hover:bg-gray-50"
                >
                    Cancel
                </button>
                <button
                    type="button"
                    onClick={handleApply}
                    disabled={isLoadingInflation && year > 1}
                    className={`px-4 py-2 text-sm rounded ${
                        isLoadingInflation && year > 1
                            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                >
                    {isLoadingInflation && year > 1 ? (
                        <div className="flex items-center">
                            Apply
                        </div>
                    ) : (
                        'Apply'
                    )}
                </button>
                <button
                    type="button"
                    onClick={handleApplytoAllYears}
                    disabled={isLoadingInflation && year > 1}
                    className={`px-4 py-2 text-sm rounded ${
                        isLoadingInflation && year > 1
                            ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                >
                    {isLoadingInflation && year > 1 ? (
                        <div className="flex items-center">
                            Apply to All Years
                        </div>
                    ) : (
                        'Apply to All Years'
                    )}
                </button>
            </div>
        </div>
    );
}
